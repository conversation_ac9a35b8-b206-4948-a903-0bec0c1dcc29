import os
import time
from collections.abc import Callable
from typing import Any

from pydantic import BaseModel

from config.loader import SettingsFactory

from ..utils import get_logger
from .aws_deepseek_client import AWSDeepSeekClient
from .azure_openai_client import AzureEmbeddingClient, AzureOpenAIClient
from .base import BaseEmbeddingClient, BaseLLMClient, EmbeddingResponse, LLMResponse
from .deepseek_client import DeepSeekClient
from .openai_client import OpenAIClient

logger = get_logger(__name__)


def get_wrapped_client():
    def wrapper(base_factory: Callable):
        def wrapped_func(*args, **kwargs):
            client = base_factory(*args, **kwargs)
            return ClientWrapper(client)

        return wrapped_func

    return wrapper


@get_wrapped_client()
def get_llm_client(
    task: str = "default",
    model: str | None = None,
    **kwargs,
) -> BaseLLMClient | BaseEmbeddingClient:
    if task == "embedding":
        # force use azure openai model
        return AzureEmbeddingClient(
            api_key=os.getenv("AZURE_API_KEY"),
            azure_endpoint=os.getenv("AZURE_ENDPOINT"),
            api_version=os.getenv("AZURE_API_VERSION"),
            model=os.getenv("EMBEDDING_MODEL"),
            **kwargs,
        )

    if not model:
        model = os.getenv("CLIENT_MODEL")

    if not model:
        raise ValueError("No valid model provided. Must be provided directly or in environment variable CLIENT_MODEL.")

    _provider = get_provider(model)

    # Wu: I copied most of code for models from MemU project,
    #     there are many duplications in parameter loading, consider fix this later
    if _provider == "openai":
        return OpenAIClient(
            api_key=os.getenv("OPENAI_API_KEY"),
            model=model,
            **kwargs,
        )
    elif _provider == "azure":
        return AzureOpenAIClient(
            api_key=os.getenv("AZURE_API_KEY"),
            azure_endpoint=os.getenv("AZURE_ENDPOINT"),
            api_version=os.getenv("AZURE_API_VERSION"),
            deployment_name=model,
            **kwargs,
        )
    elif _provider == "aws_deepseek":
        return AWSDeepSeekClient(
            model_id=os.getenv("AWS_MODEL_ID", model),
            **kwargs,
        )
    elif _provider == "deepseek":
        return DeepSeekClient(
            api_key=os.getenv("AZURE_API_KEY"),
            endpoint=os.getenv("DEEPSEEK_ENDPOINT"),
            api_version=os.getenv("DEEPSEEK_API_VERSION"),
            model_name=model,
            **kwargs,
        )


def get_provider(model: str) -> str:
    if "deepseek" in model.lower():
        if os.getenv("AWS_BEDROCK").lower() == "true":
            return "aws_deepseek"
        else:
            return "deepseek"
    elif os.getenv("AZURE").lower() == "true":
        return "azure"
    else:
        return "openai"


class TokenUsage(BaseModel):
    """Token Usage Model"""

    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0


class ClientWrapper:
    def __init__(self, client: BaseLLMClient | BaseEmbeddingClient):
        self.client = client

        self.usage = TokenUsage()
        self.action_meta = {}
        self.error_record: list[dict[str, Any]] = []
        self.debug_record: list[dict[str, Any]] = []

        settings = SettingsFactory.settings()
        self.debug_info_level = settings.LLM_DEBUG_INFO_LEVEL.lower()

        self.raw_api_record = None
        # self._hook_api_raw()
        self.set_active_hook()

    def __getattr__(self, name: str):
        return getattr(self.client, name)

    def __del__(self):
        if hasattr(self, "unhook"):
            self.unhook()

    def _update_usage(self, usage: dict[str, Any]) -> None:
        try:
            self.usage.prompt_tokens += usage.get("prompt_tokens", 0)
            self.usage.completion_tokens += usage.get("completion_tokens", 0)
            self.usage.total_tokens += usage.get("total_tokens", 0)
        except Exception as e:
            logger.exception(f"Failed to update usage with {usage}: {e!r}")

    def clear_usage(self) -> None:
        """Clear the usage of the LLM client"""
        self.usage = TokenUsage()

    def generate_embedding(self, text: str, **kwargs) -> EmbeddingResponse:
        response = self.client.generate_embedding(text, **kwargs)
        self._update_usage(response.usage)
        return response

    def chat_completion(self, messages: list[dict[str, str]], **kwargs) -> LLMResponse:
        start_time = time.time()
        response = self.client.chat_completion(messages, **kwargs)
        end_time = time.time()
        self._log_auto(response, end_time - start_time)
        return response

    def simple_chat(self, prompt: str, **kwargs) -> str:
        messages = [{"role": "user", "content": prompt}]
        start_time = time.time()
        response = self.client.chat_completion(messages, **kwargs)
        end_time = time.time()
        self._log_auto(response, end_time - start_time)
        return response.content if response.success else f"Error: {response.error}"

    def set_action_meta(self, action_meta: dict[str, Any]) -> None:
        self.action_meta = action_meta

    def _add_action_meta(self, record: dict[str, Any]) -> None:
        return record | self.action_meta

    def _hook_api_raw(self) -> None:
        if isinstance(self.client, OpenAIClient) or isinstance(self.client, AzureOpenAIClient):
            original_create = self.client.chat.completions.create

            def hooked_create(*args, **kwargs):
                response = original_create(*args, **kwargs)
                self.raw_api_record = (kwargs, response)
                return response

            self.client.chat.completions.create = hooked_create

            def unhook():
                self.client.chat.completions.create = original_create

            self.unhook = unhook
        elif isinstance(self.client, DeepSeekClient):
            original_complete = self.client.client.complete

            def hooked_complete(*args, **kwargs):
                response = original_complete(*args, **kwargs)
                self.raw_api_record = (kwargs, response)
                return response

            self.client.client.complete = hooked_complete

            def unhook():
                self.client.client.complete = original_complete

            self.unhook = unhook
        elif isinstance(self.client, AWSDeepSeekClient):
            original_converse = self.client.client.converse

            def hooked_converse(*args, **kwargs):
                response = original_converse(*args, **kwargs)
                self.raw_api_record = (kwargs, response)
                return response

            self.client.client.converse = hooked_converse

            def unhook():
                self.client.client.converse = original_converse

            self.unhook = unhook

    def set_active_hook(self) -> None:
        def active_hook(record):
            self.raw_api_record = record
        self.client._api_debug_hook = active_hook

    def _log_auto(self, response: LLMResponse, time_cost: float) -> None:
        if not response.success:
            self.error_record.append({
                "error": response.error,
                "context": response.error_context,
            })
        self._update_usage(response.usage)
        logger.info(f"token usage this call: {response.usage!r}")
        if self.debug_info_level == "all" or (self.debug_info_level == "error" and not response.success):
            self.debug_record.append(
                self._add_action_meta({
                    "params": self.raw_api_record[0],
                    # BE CAREFUL: this may take too much storage space
                    # "response_raw": self.raw_api_record[1].model_dump(),
                    # "response_raw": safe_model_dump(self.raw_api_record[1]),
                    "response": repr(response),
                    "token_usage": response.usage,
                    "time_use": time_cost,
                })
            )

def safe_model_dump(obj: Any) -> dict[str, Any]:
    """Safely serialize a Pydantic model, handling MockValSer errors"""
    try:
        # First try normal model_dump
        return obj.model_dump()
    except Exception as e:
        logger.warning(f"Failed to dump model with default settings: {e!r}")
        try:
            # Try excluding problematic fields that commonly cause issues
            return obj.model_dump(exclude={"choices", "function_call", "logprobs"})
        except Exception as e2:
            logger.warning(f"Failed to dump model with exclusions: {e2!r}")
            try:
                # Try with serialize_as_any for more flexible serialization
                return obj.model_dump(serialize_as_any=True)
            except Exception as e3:
                logger.error(f"All serialization attempts failed: {e3!r}")
                # Return basic info as fallback
                return {
                    "id": getattr(obj, "id", "unknown"),
                    "model": getattr(obj, "model", "unknown"), 
                    "object": getattr(obj, "object", "unknown"),
                    "created": getattr(obj, "created", 0),
                    "serialization_error": str(e3),
                }
