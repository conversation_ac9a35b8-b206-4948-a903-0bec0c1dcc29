from __future__ import annotations

import pendulum
from sqlalchemy import event
from sqlmodel import SQLModel

from app.modules.organization.subscription.usage_events.model import OrganizationSubscriptionUsageEvent

from .checkout_session.models import StripeCheckoutSession
from .conversation.models import Conversation
from .identity import Identity
from .identity.auth_provider import IdentityAuthProvider
from .invitation.models import Invitation
from .memory.models import Memory
from .memory.task.model import MemoryTask
from .organization import (
    Organization,
    OrganizationMember,
    OrganizationSubscription,
    Project,
    ProjectApp,
    ProjectAppApiKey,
    ProjectMember,
    ProjectMemoryCategory,
)
from .permission.models import Permission
from .platform import PlatformMember, PlatformMemberRole
from .role import Role
from .service_incident.model import ServiceIncident
from .shared.base_model import BaseModelMixin
from .stripe_product.models import StripeProduct, StripeProductPrice
from .stripe_subscription.models import StripeSubscription
from .subscription_plan import SubscriptionPlan

__all__ = [
    "Conversation",
    "Identity",
    "IdentityAuthProvider",
    "Invitation",
    "Memory",
    "MemoryTask",
    "Organization",
    "OrganizationMember",
    "OrganizationSubscription",
    "OrganizationSubscriptionUsageEvent",
    "Permission",
    "PlatformMember",
    "PlatformMemberRole",
    "Project",
    "ProjectApp",
    "ProjectAppApiKey",
    "ProjectMember",
    "ProjectMemoryCategory",
    "Role",
    "ServiceIncident",
    "StripeCheckoutSession",
    "StripeProduct",
    "StripeProductPrice",
    "StripeSubscription",
    "SubscriptionPlan",
]

metadata = SQLModel.metadata


@event.listens_for(BaseModelMixin, "before_update", propagate=True)
def auto_update_updated_at(_mapper, _connection, target):
    target.updated_at = pendulum.now("UTC")
