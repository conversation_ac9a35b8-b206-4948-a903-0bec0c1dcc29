from datetime import datetime
from typing import Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from .models import StripeWebhookEvent, WebhookEventStatus


class StripeWebhookEventCrud:
    """CRUD operations for Stripe webhook events."""

    async def get_by_stripe_event_id(
        self, stripe_event_id: str, session: AsyncSession
    ) -> StripeWebhookEvent | None:
        """Get webhook event by Stripe event ID."""
        statement = select(StripeWebhookEvent).where(
            StripeWebhookEvent.stripe_event_id == stripe_event_id
        )
        result = await session.exec(statement)
        return result.first()

    async def create_from_stripe_event(
        self,
        stripe_event_id: str,
        event_type: str,
        stripe_created: datetime,
        livemode: bool,
        event_data: dict[str, Any],
        customer_id: str | None = None,
        subscription_id: str | None = None,
        invoice_id: str | None = None,
        session: AsyncSession,
    ) -> StripeWebhookEvent:
        """Create a new webhook event record."""
        webhook_event = StripeWebhookEvent(
            stripe_event_id=stripe_event_id,
            event_type=event_type,
            stripe_created=stripe_created,
            livemode=livemode,
            event_data=event_data,
            customer_id=customer_id,
            subscription_id=subscription_id,
            invoice_id=invoice_id,
            status=WebhookEventStatus.PENDING,
        )
        
        session.add(webhook_event)
        await session.commit()
        await session.refresh(webhook_event)
        return webhook_event

    async def update_processing_status(
        self,
        webhook_event: StripeWebhookEvent,
        status: WebhookEventStatus,
        session: AsyncSession,
        error_message: str | None = None,
        processing_result: dict | None = None,
    ) -> StripeWebhookEvent:
        """Update the processing status of a webhook event."""
        if status == WebhookEventStatus.PROCESSING:
            webhook_event.mark_processing()
        elif status == WebhookEventStatus.COMPLETED:
            webhook_event.mark_completed(processing_result)
        elif status == WebhookEventStatus.FAILED:
            webhook_event.mark_failed(error_message or "Unknown error")
        elif status == WebhookEventStatus.DUPLICATE:
            webhook_event.mark_duplicate()
        
        session.add(webhook_event)
        await session.commit()
        await session.refresh(webhook_event)
        return webhook_event

    async def set_identity_and_organization(
        self,
        webhook_event: StripeWebhookEvent,
        identity_id: str | None,
        organization_id: str | None,
        session: AsyncSession,
    ) -> StripeWebhookEvent:
        """Set the identity and organization references for a webhook event."""
        webhook_event.identity_id = identity_id
        webhook_event.organization_id = organization_id
        
        session.add(webhook_event)
        await session.commit()
        await session.refresh(webhook_event)
        return webhook_event


# Singleton instance
stripe_webhook_event_crud = StripeWebhookEventCrud()
