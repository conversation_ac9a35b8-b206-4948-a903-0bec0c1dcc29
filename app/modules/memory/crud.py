from sqlalchemy import delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from ..shared.base_crud import CRUDBase
from .models import Memory, MemoryCategory, MemoryHistory
from .schema import (
    IMemoryCategoryCreate,
    IMemoryCategoryUpdate,
    IMemoryCreate,
    IMemoryHistoryCreate,
    IMemoryHistoryUpdate,
    IMemoryUpdate,
)


class CRUDMemory(CRUDBase[Memory, IMemoryCreate, IMemoryUpdate]):
    async def get_by_user_id(
        self, *, user_id: str, project_id: str, db_session: AsyncSession | None = None
    ) -> list[Memory]:
        """Get all memories by user ID"""
        stmt = select(Memory).where(
            Memory.user_id == user_id,
            Memory.project_id == project_id,
        )
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_user_and_agent(
        self, *, user_id: str, agent_id: str | None = None, project_id: str, db_session: AsyncSession | None = None
    ) -> list[Memory]:
        """Get memories by user ID and agent ID"""
        stmt = select(Memory).where(
            Memory.user_id == user_id,
            Memory.project_id == project_id,
        )
        if agent_id:
            stmt = stmt.where(Memory.agent_id == agent_id)
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_agent_id(
        self, *, agent_id: str, project_id: str, db_session: AsyncSession | None = None
    ) -> list[Memory]:
        """Get all memories by agent ID"""
        stmt = select(Memory).where(
            Memory.agent_id == agent_id,
            Memory.project_id == project_id,
        )
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_category(
        self, *, category: str, user_id: str, agent_id: str | None, project_id: str, db_session: AsyncSession
    ) -> list[Memory]:
        """Get memories by category and user ID"""
        stmt = select(Memory).where(
            Memory.category == category,
            Memory.user_id == user_id,
            Memory.project_id == project_id,
        )
        if agent_id:
            stmt = stmt.where(Memory.agent_id == agent_id)
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_category_top(
        self,
        *,
        category: str,
        user_id: str,
        agent_id: str | None,
        project_id: str,
        db_session: AsyncSession,
        top_n: int = 100,
        order: str = "desc",
        order_by: str = "happened_at",
    ) -> list[Memory]:
        """Get top N memories by category and user ID"""
        stmt = select(Memory).where(
            Memory.category == category,
            Memory.user_id == user_id,
            Memory.project_id == project_id,
        )
        if agent_id:
            stmt = stmt.where(Memory.agent_id == agent_id)
        stmt = stmt.order_by(getattr(Memory, order_by).desc() if order == "desc" else getattr(Memory, order_by).asc())
        stmt = stmt.limit(top_n)
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_user_agent_category(
        self, *, user_id: str, agent_id: str, category: str, project_id: str, db_session: AsyncSession
    ) -> list[Memory]:
        """Get memories by user ID, agent ID, and category"""
        stmt = select(Memory).where(
            Memory.user_id == user_id,
            Memory.agent_id == agent_id,
            Memory.category == category,
            Memory.project_id == project_id,
        )
        result = await db_session.execute(stmt)
        return result.scalars().all()

    # Note: the possibility of hash collision for 6-characters memory_id is low, but not zero
    #       (50% chance to collision if there're 65,971 memories)
    #       get by memory_id may raise MultipleResultsFound if there are multiple matches
    async def get_by_user_agent_category_memory_id(
        self, *, user_id: str, agent_id: str, category: str, memory_id: str, project_id: str, db_session: AsyncSession
    ) -> Memory | None:
        """Get a memory by user ID, agent ID, category, and memory ID"""
        stmt = select(Memory).where(
            Memory.user_id == user_id,
            Memory.agent_id == agent_id,
            Memory.category == category,
            Memory.memory_id == memory_id,
            Memory.project_id == project_id,
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    async def remove(
        self, *, user_id: str, agent_id: str, category: str, memory_id: str, db_session: AsyncSession
    ) -> None:
        """Remove a memory by user ID, agent ID, category, and memory ID"""
        response = await db_session.execute(
            select(Memory).where(
                Memory.user_id == user_id,
                Memory.agent_id == agent_id,
                Memory.category == category,
                Memory.memory_id == memory_id,
            )
        )
        memory = response.scalar_one()
        await db_session.delete(memory)
        await db_session.commit()

    async def similarity_search(
        self,
        *,
        query_embedding: list[float],
        user_id: str,
        agent_id: str | None = None,
        project_id: str,
        category: str | None = None,
        categories: list[str] | None = None,
        exclude_memory_id: str | None = None,
        limit: int = 10,
        similarity_threshold: float = 0.7,
        db_session: AsyncSession | None = None,
    ) -> list[tuple[Memory, float]]:
        """Find similar memories using cosine similarity with pgvector"""

        # Build base query using SQLAlchemy for safety
        # Include cosine_distance as a column in the SELECT
        distance_col = Memory.embedding.cosine_distance(query_embedding)
        stmt = select(Memory, distance_col).where(
            Memory.user_id == user_id,
            Memory.project_id == project_id,
            Memory.embedding.is_not(None),
        )

        # Add optional filters - all at database level for efficiency
        if agent_id:
            stmt = stmt.where(Memory.agent_id == agent_id)

        # Category filtering - single category or multiple categories
        if category:
            stmt = stmt.where(Memory.category == category)
        elif categories:
            stmt = stmt.where(Memory.category.in_(categories))

        # Exclude specific memory (e.g., the query memory itself)
        if exclude_memory_id:
            stmt = stmt.where(Memory.memory_id != exclude_memory_id)

        # Add similarity search with cosine distance using pgvector
        # cosine distance = 1 - cosine similarity, so we filter by distance < (1 - threshold)
        stmt = stmt.where(distance_col < (1 - similarity_threshold))

        # Order by similarity (closest first) using pgvector cosine distance
        stmt = stmt.order_by(distance_col)

        # Apply limit
        stmt = stmt.limit(limit)

        result = await db_session.execute(stmt)

        # Process results - now we get tuples of (Memory, distance)
        memories_with_similarity = []
        for row in result:
            memory, distance = row
            # Convert distance to similarity (cosine similarity = 1 - cosine distance)
            similarity = 1.0 - distance
            memories_with_similarity.append((memory, similarity))

        return memories_with_similarity

    async def nearest_neighbors(
        self,
        *,
        query_embedding: list[float],
        user_id: str,
        agent_id: str | None = None,
        project_id: str,
        category: str | None = None,
        limit: int = 5,
        db_session: AsyncSession | None = None,
    ) -> list[Memory]:
        """Find nearest neighbor memories using L2 distance"""

        # Build base query
        stmt = select(Memory).where(
            Memory.user_id == user_id,
            Memory.project_id == project_id,
            Memory.embedding.is_not(None),
        )

        # Add optional filters
        if agent_id:
            stmt = stmt.where(Memory.agent_id == agent_id)
        if category:
            stmt = stmt.where(Memory.category == category)

        # Order by L2 distance (closest first)
        stmt = stmt.order_by(Memory.embedding.l2_distance(query_embedding))

        # Apply limit
        stmt = stmt.limit(limit)

        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def delete_by_user_and_agent(
        self,
        *,
        user_id: str,
        agent_id: str | None,
        project_id: str,
        db_session: AsyncSession,
    ) -> int:
        """Delete memories by user_id and optional agent_id within a project. Returns number deleted."""
        stmt = delete(Memory).where(
            Memory.user_id == user_id,
            Memory.project_id == project_id,
        )
        if agent_id:
            stmt = stmt.where(Memory.agent_id == agent_id)

        result = await db_session.execute(stmt)
        await db_session.commit()
        return result.rowcount or 0


class CRUDMemoryHistory(CRUDBase[MemoryHistory, IMemoryHistoryCreate, IMemoryHistoryUpdate]):
    async def get_by_memory_id(
        self, *, memory_id: str, project_id: str, db_session: AsyncSession | None = None
    ) -> list[MemoryHistory]:
        """Get all history records by memory ID"""
        stmt = (
            select(MemoryHistory)
            .where(MemoryHistory.memory_id == memory_id, MemoryHistory.project_id == project_id)
            .order_by(MemoryHistory.timestamp.desc())
        )
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_user_id(
        self, *, user_id: str, project_id: str, db_session: AsyncSession | None = None
    ) -> list[MemoryHistory]:
        """Get all memory history by user ID"""
        stmt = (
            select(MemoryHistory)
            .where(MemoryHistory.user_id == user_id, MemoryHistory.project_id == project_id)
            .order_by(MemoryHistory.timestamp.desc())
        )
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_user_and_agent(
        self, *, user_id: str, agent_id: str, project_id: str, db_session: AsyncSession | None = None
    ) -> list[MemoryHistory]:
        """Get memory history by user ID and agent ID"""
        stmt = (
            select(MemoryHistory)
            .where(
                MemoryHistory.user_id == user_id,
                MemoryHistory.agent_id == agent_id,
                MemoryHistory.project_id == project_id,
            )
            .order_by(MemoryHistory.timestamp.desc())
        )
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_action(
        self, *, action: str, user_id: str, project_id: str, db_session: AsyncSession | None = None
    ) -> list[MemoryHistory]:
        """Get memory history by action type and user ID"""
        stmt = (
            select(MemoryHistory)
            .where(
                MemoryHistory.action == action,
                MemoryHistory.user_id == user_id,
                MemoryHistory.project_id == project_id,
            )
            .order_by(MemoryHistory.timestamp.desc())
        )
        result = await db_session.execute(stmt)
        return result.scalars().all()


class CRUDMemoryCategory(CRUDBase[MemoryCategory, IMemoryCategoryCreate, IMemoryCategoryUpdate]):
    async def get_by_user_agent_group(
        self,
        *,
        user_id: str,
        agent_id: str | None = None,
        group: str | None = None,
        project_id: str,
        db_session: AsyncSession,
    ) -> list[MemoryCategory]:
        """Get memory categories by user ID, agent ID, and category group"""
        stmt = select(MemoryCategory).where(
            MemoryCategory.user_id == user_id,
            MemoryCategory.project_id == project_id,
        )
        if agent_id:
            stmt = stmt.where(MemoryCategory.agent_id == agent_id)
        if group:
            stmt = stmt.where(MemoryCategory.group == group)
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_user_name_all_agent(
        self, *, user_id: str, name: str, project_id: str, db_session: AsyncSession
    ) -> list[MemoryCategory]:
        """Get memory category by name"""
        stmt = select(MemoryCategory).where(
            MemoryCategory.user_id == user_id,
            MemoryCategory.project_id == project_id,
            MemoryCategory.name == name,
        )
        result = await db_session.execute(stmt)
        return result.scalars().all()

    async def get_by_user_agent_name(
        self, *, user_id: str, agent_id: str, name: str, project_id: str, db_session: AsyncSession
    ) -> MemoryCategory | None:
        """Get memory category by name"""
        stmt = select(MemoryCategory).where(
            MemoryCategory.user_id == user_id,
            MemoryCategory.agent_id == agent_id,
            MemoryCategory.name == name,
            MemoryCategory.project_id == project_id,
        )
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()

    # async def get_default_categories(self, *, project_id: str, db_session: AsyncSession) -> list[MemoryCategory]:
    #     """Get default memory categories for a project"""
    #     return await ProjectMemCategoryRepo.get_active_categories(
    #         project_id=project_id,
    #         db_session=db_session,
    #     )


MemoryRepo = CRUDMemory(Memory)
MemoryHistoryRepo = CRUDMemoryHistory(MemoryHistory)
MemoryCategoryRepo = CRUDMemoryCategory(MemoryCategory)
